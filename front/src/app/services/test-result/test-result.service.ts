import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';
import { OidcSecurityService } from 'angular-auth-oidc-client';
import { switchMap } from 'rxjs/operators';

export interface TestResultWithScore {
  id: string;
  testId: string;
  userId: string;
  candidateName: string;
  candidateEmail?: string; // Email du candidat pour les notifications
  testName: string;
  submittedAt: Date;
  totalQuestions: number;
  correctAnswers: number;
  scorePercentage: number;
}

export interface TestResultDisplay {
  id: string;
  job: string;
  name: string;
  date: string;
  timeLimit: string;
  timePassed: string;
  score: string;
  correctAnswers: number;
  totalQuestions: number;
  userId?: string; // Ajout pour la navigation vers le profil
  profileImage?: string; // Ajout pour l'image de profil
  status?: 'pending' | 'accepted' | 'rejected'; // Statut du candidat
  candidateEmail?: string; // Email pour l'envoi de notifications
}

@Injectable({
  providedIn: 'root'
})
export class TestResultService {
  private apiUrl = `${environment.apiUrl}/tests`;

  constructor(
    private http: HttpClient,
    private oidcSecurityService: OidcSecurityService
  ) {}

  /**
   * Récupère tous les résultats de test avec scores calculés
   */
  getAllTestResults(): Observable<TestResultWithScore[]> {
    console.log('🔄 Service: Récupération des résultats de test depuis:', `${this.apiUrl}/results`);

    return this.oidcSecurityService.getAccessToken().pipe(
      switchMap((token) => {
        console.log('🔑 Service: Token d\'authentification obtenu:', token ? 'Présent' : 'Absent');
        const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
        console.log('📡 Service: Appel API vers:', `${this.apiUrl}/results`);

        return this.http.get<TestResultWithScore[]>(`${this.apiUrl}/results`, { headers }).pipe(
          tap((data) => {
            console.log('📥 Service: Données brutes reçues du backend:', data);
            console.log('📊 Service: Nombre de résultats reçus:', data?.length || 0);
            if (data && Array.isArray(data) && data.length > 0) {
              console.log('🔍 Service: Premier résultat exemple:', data[0]);
            }
          })
        );
      })
    );
  }

  /**
   * Récupère un résultat de test spécifique
   */
  getTestResult(resultId: string): Observable<TestResultWithScore> {
    return this.oidcSecurityService.getAccessToken().pipe(
      switchMap((token) => {
        const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
        return this.http.get<TestResultWithScore>(`${this.apiUrl}/results/${resultId}`, { headers });
      })
    );
  }
  /**
   * Supprime un résultat de test
   */
  deleteTestResult(resultId: string): Observable<any> {
    return this.oidcSecurityService.getAccessToken().pipe(
      switchMap((token) => {
        const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
        return this.http.delete(`${this.apiUrl}/results/${resultId}`, { headers });
      })
    );
  }



  /**
   * Accepte un candidat et envoie un email de notification
   */
  acceptCandidate(resultId: string, candidateEmail: string): Observable<any> {
    return this.oidcSecurityService.getAccessToken().pipe(
      switchMap((token) => {
        const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
        const body = {
          action: 'accept',
          candidateEmail: candidateEmail,
          resultId: resultId
        };
        return this.http.post(`${this.apiUrl}/tests/results/${resultId}/status`, body, { headers });
      })
    );
  }

  /**
   * Refuse un candidat et envoie un email de notification
   */
  rejectCandidate(resultId: string, candidateEmail: string): Observable<any> {
    return this.oidcSecurityService.getAccessToken().pipe(
      switchMap((token) => {
        const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
        const body = {
          action: 'reject',
          candidateEmail: candidateEmail,
          resultId: resultId
        };
        return this.http.post(`${this.apiUrl}/tests/results/${resultId}/status`, body, { headers });
      })
    );
  }

  /**
   * Transforme les données du backend en format d'affichage
   */
  transformToDisplayFormat(results: TestResultWithScore[]): TestResultDisplay[] {
    return results.map(result => ({
      id: result.id,
      job: result.testName || 'Test technique',
      name: result.candidateName,
      date: this.formatDate(result.submittedAt),
      timeLimit: '1h', // Valeur par défaut, peut être récupérée du test si nécessaire
      timePassed: 'N/A', // Peut être calculé si on stocke le temps de début
      score: `${Math.round(result.scorePercentage)}%`,
      correctAnswers: result.correctAnswers,
      totalQuestions: result.totalQuestions,
      userId: result.userId,
      profileImage: undefined, // Peut être ajouté plus tard
      status: 'pending', // Statut par défaut
      candidateEmail: result.candidateEmail || '' // Email du candidat
    }));
  }

  /**
   * Formate une date en format français
   */
  private formatDate(date: Date): string {
    if (!date) return 'N/A';

    const d = new Date(date);
    const day = d.getDate().toString().padStart(2, '0');
    const month = (d.getMonth() + 1).toString().padStart(2, '0');
    const year = d.getFullYear();

    return `${day}/${month}/${year}`;
  }

  /**
   * Calcule la couleur du score selon le pourcentage
   */
  getScoreColor(scorePercentage: number): string {
    if (scorePercentage >= 80) return '#22C55E'; // Vert
    if (scorePercentage >= 60) return '#F59E0B'; // Orange
    return '#EF4444'; // Rouge
  }

  /**
   * Retourne le statut textuel du score
   */
  getScoreStatus(scorePercentage: number): string {
    if (scorePercentage >= 80) return 'Excellent';
    if (scorePercentage >= 60) return 'Bien';
    if (scorePercentage >= 40) return 'Moyen';
    return 'Faible';
  }
}
