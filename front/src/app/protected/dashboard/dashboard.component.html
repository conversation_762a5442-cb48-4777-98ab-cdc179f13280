<div class="dashboard">
  <app-sidebar-recruiter></app-sidebar-recruiter>

  <main class="dashboard-main">
    <!-- Header Section -->
    <header class="dashboard-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="dashboard-title">
            <mat-icon class="title-icon">dashboard</mat-icon>
            Dashboard Recruteur
          </h1>
          <p class="dashboard-subtitle">Gérez vos recrutements avec efficacité</p>
        </div>
        <div class="header-right">
          <div class="search-container">
            <mat-icon class="search-icon">search</mat-icon>
            <input class="search-bar" type="text" placeholder="Rechercher..." (input)="filterDashboard($event)">
          </div>

          <!-- Notifications Button -->
          <button mat-button class="notification-button" (click)="toggleNotifications()">
            <mat-icon>notifications</mat-icon>
            <span class="notification-badge" *ngIf="notifications.length > 0">{{ notifications.length }}</span>
          </button>

          <!-- Refresh Button -->
          <button mat-button class="refresh-button" (click)="refreshDashboard()" [disabled]="isLoading">
            <mat-icon [class.spinning]="isLoading">refresh</mat-icon>
          </button>

          <!-- Export Button -->
          <button mat-button class="export-button" (click)="exportDashboardData()">
            <mat-icon>download</mat-icon>
          </button>

          <button mat-button class="profile-button" (click)="goToSettings()">
            <mat-icon>account_circle</mat-icon>
            <span>Mon Profil</span>
          </button>
        </div>
      </div>
    </header>

    <div class="card-container">
      <mat-card class="dashboard-card" (click)="goToWorkspace()">
        <mat-card-header>
          <mat-icon class="card-icon">work</mat-icon>
          <mat-card-title>Voir votre workspace</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <p>Accédez à votre espace de travail</p>
          <button mat-button color="primary" class="card-button">Accéder</button>
        </mat-card-content>
      </mat-card>

      <mat-card class="dashboard-card" (click)="goToPosts()">
        <mat-card-header>
          <mat-icon class="card-icon">post_add</mat-icon>
          <mat-card-title>Voir vos posts</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <p>Découvrez vos offres d’emploi publiées et leur statut.</p>
          <button mat-button color="primary" class="card-button">Voir plus</button>
        </mat-card-content>
      </mat-card>

      <mat-card class="dashboard-card" (click)="goToSettings()">
        <mat-card-header>
          <mat-icon class="card-icon">settings</mat-icon>
          <mat-card-title>Configurer votre compte</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <p>Modifiez vos paramètres et préférences.</p>
          <button mat-button color="primary" class="card-button">Configurer</button>
        </mat-card-content>
      </mat-card>

    </div>

    <!-- Statistics Overview -->
    <section class="stats-overview">
      <div class="stats-grid">
        <div class="stat-card" [style.border-left-color]="getStatColor('totalPosts')">
          <div class="stat-content">
            <div class="stat-header">
              <mat-icon [style.color]="getStatColor('totalPosts')">{{ getStatIcon('totalPosts') }}</mat-icon>
              <span class="stat-label">Total Offres</span>
            </div>
            <div class="stat-value">{{ dashboardStats.totalPosts }}</div>
            <div class="stat-change positive">+{{ dashboardStats.activePosts }} actives</div>
          </div>
        </div>

        <div class="stat-card" [style.border-left-color]="getStatColor('totalTests')">
          <div class="stat-content">
            <div class="stat-header">
              <mat-icon [style.color]="getStatColor('totalTests')">{{ getStatIcon('totalTests') }}</mat-icon>
              <span class="stat-label">Tests Créés</span>
            </div>
            <div class="stat-value">{{ dashboardStats.totalTests }}</div>
            <div class="stat-change positive">{{ dashboardStats.completedTests }} complétés</div>
          </div>
        </div>

        <div class="stat-card" [style.border-left-color]="getStatColor('averageScore')">
          <div class="stat-content">
            <div class="stat-header">
              <mat-icon [style.color]="getStatColor('averageScore')">{{ getStatIcon('averageScore') }}</mat-icon>
              <span class="stat-label">Score Moyen</span>
            </div>
            <div class="stat-value">{{ dashboardStats.averageScore }}%</div>
            <div class="stat-change neutral">Performance globale</div>
          </div>
        </div>

        <div class="stat-card" [style.border-left-color]="getStatColor('recentActivity')">
          <div class="stat-content">
            <div class="stat-header">
              <mat-icon [style.color]="getStatColor('recentActivity')">{{ getStatIcon('recentActivity') }}</mat-icon>
              <span class="stat-label">Activité Récente</span>
            </div>
            <div class="stat-value">{{ dashboardStats.recentActivity }}</div>
            <div class="stat-change neutral">Cette semaine</div>
          </div>
        </div>
      </div>
    </section>

    <!-- Quick Actions -->
    <section class="quick-actions">
      <h2 class="section-title">
        <mat-icon>flash_on</mat-icon>
        Actions Rapides
      </h2>
      <div class="actions-grid">
        <div class="action-card"
             *ngFor="let action of quickActions"
             (click)="executeQuickAction(action.action)"
             [style.background]="'linear-gradient(135deg, ' + action.color + '15, ' + action.color + '05)'">
          <div class="action-icon" [style.background-color]="action.color">
            <mat-icon>{{ action.icon }}</mat-icon>
          </div>
          <div class="action-content">
            <h3>{{ action.title }}</h3>
            <p>{{ action.description }}</p>
          </div>
          <mat-icon class="action-arrow">arrow_forward</mat-icon>
        </div>
      </div>
    </section>

    <!-- Recent Activity -->
    <section class="recent-activity">
      <div class="activity-grid">
        <!-- Recent Posts -->
        <div class="activity-section">
          <h3 class="activity-title">
            <mat-icon>work</mat-icon>
            Offres Récentes
          </h3>
          <div class="activity-list" *ngIf="!isLoading; else loadingTemplate">
            <div class="activity-item" *ngFor="let post of recentPosts.slice(0, 3)">
              <div class="activity-icon">
                <mat-icon>work_outline</mat-icon>
              </div>
              <div class="activity-details">
                <h4>{{ post.titre }}</h4>
                <p>{{ post.entreprise }}</p>
                <span class="activity-date">{{ post.datePublication | date:'short' }}</span>
              </div>
              <div class="activity-status" [class.active]="!post.archived">
                {{ post.archived ? 'Archivée' : 'Active' }}
              </div>
            </div>
          </div>
        </div>

        <!-- Recent Tests -->
        <div class="activity-section">
          <h3 class="activity-title">
            <mat-icon>assignment</mat-icon>
            Tests Récents
          </h3>
          <div class="activity-list" *ngIf="!isLoading; else loadingTemplate">
            <div class="activity-item" *ngFor="let test of recentTests.slice(0, 3)">
              <div class="activity-icon">
                <mat-icon>assignment_turned_in</mat-icon>
              </div>
              <div class="activity-details">
                <h4>{{ test.candidateName }}</h4>
                <p>{{ test.testName }}</p>
                <span class="activity-date">{{ test.submittedAt | date:'short' }}</span>
              </div>
              <div class="activity-score">
                {{ test.scorePercentage }}%
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Loading Template -->
    <ng-template #loadingTemplate>
      <div class="loading-container">
        <div class="loading-spinner">
          <mat-icon>hourglass_empty</mat-icon>
        </div>
        <p>Chargement des données...</p>
      </div>
    </ng-template>

  </main>
</div>

<router-outlet></router-outlet>
