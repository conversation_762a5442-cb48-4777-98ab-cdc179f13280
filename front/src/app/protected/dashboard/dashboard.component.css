/* Global container */
.dashboard {
  display: grid;
  grid-template-columns: 250px 1fr;
  grid-template-areas: "sidebar main";
  min-height: 100vh;
  background:
    linear-gradient(145deg, #0f0f23 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #001040FF 100%),
    radial-gradient(circle at 20% 30%, rgba(255, 107, 53, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 70%, rgba(59, 130, 246, 0.08) 0%, transparent 50%);
  font-family: 'Inter', 'SF Pro Display', 'Segoe UI', sans-serif;
  overflow-x: hidden;
  position: relative;
}

.dashboard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    repeating-linear-gradient(
      90deg,
      transparent,
      transparent 98px,
      rgba(255, 255, 255, 0.02) 100px
    ),
    repeating-linear-gradient(
      0deg,
      transparent,
      transparent 98px,
      rgba(255, 255, 255, 0.02) 100px
    );
  pointer-events: none;
  z-index: 0;
}

/* Sidebar */
app-sidebar-recruiter {
  grid-area: sidebar;
  z-index: 100;
  position: relative;
}

/* Main content */
.dashboard-main {
  grid-area: main;
  display: flex;
  flex-direction: column;
  background:
    linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.03) 100%);
  backdrop-filter: blur(30px) saturate(150%);
  border-radius: 32px 0 0 32px;
  margin: 16px 0 16px 16px;
  position: relative;
  z-index: 1;
  overflow-y: auto;
  max-height: calc(100vh - 32px);
  border: 1px solid rgba(255, 255, 255, 0.12);
  box-shadow:
    0 24px 48px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

/* Header */
.dashboard-header {
  background:
    linear-gradient(135deg, rgba(15, 15, 35, 0.9) 0%, rgba(26, 26, 46, 0.8) 100%);
  backdrop-filter: blur(25px) saturate(180%);
  border-bottom: 2px solid rgba(255, 107, 53, 0.3);
  padding: 28px 36px;
  position: sticky;
  top: 0;
  z-index: 50;
  border-radius: 32px 0 0 0;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.header-left {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

/* Title */
.dashboard-title {
  display: flex;
  align-items: center;
  gap: 20px;
  font-size: 38px;
  font-weight: 800;
  background: linear-gradient(135deg, #ffffff 0%, #FF6B35 50%, #ffffff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
  text-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
  position: relative;
}

.dashboard-title::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 80px;
  height: 4px;
  background: linear-gradient(90deg, #FF6B35, #3b82f6, transparent);
  border-radius: 2px;
  animation: titleGlow 3s ease-in-out infinite;
}

@keyframes titleGlow {
  0%, 100% { opacity: 0.7; transform: scaleX(1); }
  50% { opacity: 1; transform: scaleX(1.1); }
}

.title-icon {
  font-size: 44px;
  width: 44px;
  height: 44px;
  background: linear-gradient(135deg, #FF6B35, #3b82f6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  filter: drop-shadow(0 4px 12px rgba(255, 107, 53, 0.5));
  animation: iconFloat 4s ease-in-out infinite;
}

@keyframes iconFloat {
  0%, 100% { transform: translateY(0) rotate(0deg); }
  25% { transform: translateY(-4px) rotate(2deg); }
  75% { transform: translateY(2px) rotate(-1deg); }
}

.dashboard-subtitle {
  color: rgba(255, 255, 255, 0.95);
  font-size: 18px;
  font-weight: 500;
  margin: 0;
  letter-spacing: 1px;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
}

/* Search container */
.search-container {
  display: flex;
  align-items: center;
  background:
    linear-gradient(135deg, rgba(15, 15, 35, 0.8) 0%, rgba(26, 26, 46, 0.6) 100%);
  backdrop-filter: blur(20px) saturate(180%);
  border: 2px solid transparent;
  background-clip: padding-box;
  border-radius: 20px;
  padding: 16px 20px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.search-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #FF6B35, #3b82f6, #FF6B35);
  border-radius: 20px;
  padding: 2px;
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
  opacity: 0;
  transition: opacity 0.4s ease;
}

.search-container:hover::before,
.search-container:focus-within::before {
  opacity: 1;
}

.search-container:hover,
.search-container:focus-within {
  transform: translateY(-2px) scale(1.02);
  box-shadow:
    0 12px 40px rgba(255, 107, 53, 0.3),
    0 0 0 1px rgba(255, 107, 53, 0.2);
}

.search-icon {
  color: #FF6B35;
  font-size: 20px;
  margin-right: 8px;
  transition: all 0.3s ease;
}

.search-bar {
  border: none;
  background: transparent;
  padding: 0;
  width: 220px;
  font-size: 14px;
  color: #ffffff;
  outline: none;
  font-weight: 400;
}

.search-bar::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

/* Profile button */
.profile-button {
  display: flex;
  align-items: center;
  gap: 10px;
  background: linear-gradient(135deg, #001040FF, #001660FF);
  color: #ffffff !important;
  border-radius: 12px;
  padding: 12px 20px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.profile-button:hover {
  background: linear-gradient(135deg, #FF6B35, #001660FF);
  transform: translateY(-2px);
}

.profile-button mat-icon {
  font-size: 20px;
  width: 20px;
  height: 20px;
  color: #ffffff;
}

/* Header Buttons */
.notification-button,
.refresh-button,
.export-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  color: #ffffff;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.notification-button:hover,
.refresh-button:hover,
.export-button:hover {
  background: rgba(255, 107, 53, 0.2);
  border-color: rgba(255, 107, 53, 0.4);
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(255, 107, 53, 0.3);
}

.notification-badge {
  position: absolute;
  top: -4px;
  right: -4px;
  background: #FF6B35;
  color: #ffffff;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  font-size: 12px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.spinning {
  animation: spin 1s linear infinite;
}

/* Notifications Panel */
.notifications-panel {
  position: absolute;
  top: 100px;
  right: 32px;
  width: 400px;
  max-height: 500px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  overflow: hidden;
}

.notifications-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.notifications-header h3 {
  color: #ffffff;
  font-size: 18px;
  font-weight: 600;
  margin: 0;
}

.notifications-list {
  max-height: 400px;
  overflow-y: auto;
  padding: 16px;
}

.notification-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 16px;
  margin-bottom: 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border-left: 4px solid;
  transition: all 0.3s ease;
}

.notification-item:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateX(4px);
}

.notification-item.success {
  border-left-color: #22c55e;
}

.notification-item.info {
  border-left-color: #3b82f6;
}

.notification-item.warning {
  border-left-color: #f59e0b;
}

.notification-item.error {
  border-left-color: #ef4444;
}

.notification-content {
  flex: 1;
}

.notification-content h4 {
  color: #ffffff;
  font-size: 14px;
  font-weight: 600;
  margin: 0 0 4px 0;
}

.notification-content p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 13px;
  margin: 0 0 8px 0;
  line-height: 1.4;
}

.notification-time {
  color: rgba(255, 255, 255, 0.5);
  font-size: 11px;
}

/* Card container */
.card-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  padding: 32px;
}

/* Statistics Overview */
.stats-overview {
  padding: 32px;
  margin-bottom: 24px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
}

.stat-card {
  background:
    linear-gradient(145deg, rgba(15, 15, 35, 0.9) 0%, rgba(26, 26, 46, 0.7) 100%);
  backdrop-filter: blur(25px) saturate(200%);
  border: 2px solid transparent;
  background-clip: padding-box;
  border-radius: 24px;
  padding: 32px;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  box-shadow:
    0 16px 40px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--card-color, #FF6B35), transparent, var(--card-color, #FF6B35));
  border-radius: 24px;
  padding: 2px;
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
  opacity: 0;
  transition: opacity 0.5s ease;
}

.stat-card::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background:
    radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  transform: scale(0);
  transition: transform 0.6s ease;
  border-radius: 50%;
}

.stat-card:hover::before {
  opacity: 1;
}

.stat-card:hover::after {
  transform: scale(1);
}

.stat-card:hover {
  transform: translateY(-8px) scale(1.03);
  box-shadow:
    0 24px 60px rgba(0, 0, 0, 0.5),
    0 0 0 1px rgba(255, 107, 53, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.stat-content {
  position: relative;
  z-index: 1;
}

.stat-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.stat-header mat-icon {
  font-size: 28px;
  width: 28px;
  height: 28px;
  background: linear-gradient(135deg, var(--icon-color, #FF6B35), #3b82f6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  filter: drop-shadow(0 2px 8px rgba(255, 107, 53, 0.4));
}

.stat-label {
  color: rgba(255, 255, 255, 0.9);
  font-size: 15px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1.2px;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.stat-value {
  font-size: 44px;
  font-weight: 900;
  background: linear-gradient(135deg, #ffffff 0%, rgba(255, 255, 255, 0.8) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 12px;
  text-shadow: 0 4px 12px rgba(255, 255, 255, 0.3);
  position: relative;
  display: inline-block;
}

.stat-value::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, var(--value-color, #FF6B35), transparent);
  border-radius: 1px;
  opacity: 0.7;
}

.stat-change {
  font-size: 12px;
  font-weight: 500;
  padding: 4px 8px;
  border-radius: 6px;
  display: inline-block;
}

.stat-change.positive {
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
}

.stat-change.neutral {
  background: rgba(156, 163, 175, 0.2);
  color: #9ca3af;
}

/* Quick Actions */
.quick-actions {
  padding: 32px;
  margin-bottom: 24px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 24px;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 24px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.section-title mat-icon {
  font-size: 28px;
  width: 28px;
  height: 28px;
  color: #FF6B35;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.action-card {
  background:
    linear-gradient(145deg, rgba(15, 15, 35, 0.8) 0%, rgba(26, 26, 46, 0.6) 100%);
  backdrop-filter: blur(25px) saturate(180%);
  border: 2px solid transparent;
  background-clip: padding-box;
  border-radius: 20px;
  padding: 28px;
  cursor: pointer;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  gap: 20px;
  position: relative;
  overflow: hidden;
  box-shadow:
    0 12px 32px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.action-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--action-color, #FF6B35), #3b82f6);
  border-radius: 20px;
  padding: 2px;
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
  opacity: 0;
  transition: opacity 0.5s ease;
}

.action-card::after {
  content: '';
  position: absolute;
  top: 50%;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transform: translateY(-50%) skewX(-20deg);
  transition: left 0.8s ease;
}

.action-card:hover::before {
  opacity: 1;
}

.action-card:hover::after {
  left: 100%;
}

.action-card:hover {
  transform: translateY(-6px) scale(1.02);
  box-shadow:
    0 20px 50px rgba(0, 0, 0, 0.4),
    0 0 0 1px rgba(255, 107, 53, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.action-icon {
  width: 64px;
  height: 64px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  background: linear-gradient(135deg, var(--action-color, #FF6B35), #3b82f6);
  box-shadow:
    0 8px 24px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.action-icon::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), transparent);
  border-radius: 16px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.action-card:hover .action-icon::before {
  opacity: 1;
}

.action-icon mat-icon {
  font-size: 32px;
  width: 32px;
  height: 32px;
  color: #ffffff;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
  z-index: 1;
  position: relative;
}

.action-content {
  flex: 1;
  z-index: 1;
  position: relative;
}

.action-content h3 {
  font-size: 20px;
  font-weight: 700;
  background: linear-gradient(135deg, #ffffff 0%, rgba(255, 255, 255, 0.9) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0 0 8px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.action-content p {
  font-size: 15px;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  line-height: 1.5;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.action-arrow {
  color: rgba(255, 255, 255, 0.6);
  font-size: 24px;
  width: 24px;
  height: 24px;
  transition: all 0.4s ease;
  z-index: 1;
  position: relative;
}

.action-card:hover .action-arrow {
  color: #ffffff;
  transform: translateX(6px) scale(1.1);
}

/* Recent Activity */
.recent-activity {
  padding: 32px;
  margin-bottom: 24px;
}

.activity-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
}

.activity-section {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 24px;
  transition: all 0.3s ease;
}

.activity-section:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
}

.activity-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 18px;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.activity-title mat-icon {
  font-size: 22px;
  width: 22px;
  height: 22px;
  color: #FF6B35;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.activity-item:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateX(4px);
}

.activity-icon {
  width: 40px;
  height: 40px;
  background: rgba(255, 107, 53, 0.2);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.activity-icon mat-icon {
  font-size: 20px;
  width: 20px;
  height: 20px;
  color: #FF6B35;
}

.activity-details {
  flex: 1;
}

.activity-details h4 {
  font-size: 16px;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 4px 0;
}

.activity-details p {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  margin: 0 0 4px 0;
}

.activity-date {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.5);
}

.activity-status {
  padding: 4px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  background: rgba(156, 163, 175, 0.2);
  color: #9ca3af;
}

.activity-status.active {
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
}

.activity-score {
  padding: 4px 12px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  background: rgba(59, 130, 246, 0.2);
  color: #3b82f6;
}

/* Loading */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: rgba(255, 255, 255, 0.7);
}

.loading-spinner {
  animation: spin 2s linear infinite;
  margin-bottom: 16px;
}

.loading-spinner mat-icon {
  font-size: 32px;
  width: 32px;
  height: 32px;
  color: #FF6B35;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Responsive */
@media (max-width: 1024px) {
  .dashboard {
    grid-template-columns: 220px 1fr;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .header-right {
    width: 100%;
    justify-content: space-between;
  }

  .search-bar {
    width: 180px;
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
  }

  .actions-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 16px;
  }

  .activity-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .stats-overview,
  .quick-actions,
  .recent-activity {
    padding: 24px;
  }
}

@media (max-width: 768px) {
  .dashboard {
    grid-template-columns: 1fr;
    grid-template-areas: "main";
  }

  app-sidebar-recruiter {
    display: none;
  }

  .dashboard-main {
    border-radius: 0;
  }

  .dashboard-header {
    padding: 16px 20px;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-left {
    text-align: center;
  }

  .dashboard-title {
    font-size: 28px;
    justify-content: center;
  }

  .header-right {
    flex-direction: column;
    gap: 12px;
  }

  .search-container {
    width: 100%;
  }

  .search-bar {
    width: 100%;
  }

  .profile-button {
    width: 100%;
    justify-content: center;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .stat-value {
    font-size: 28px;
  }

  .actions-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .action-card {
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }

  .activity-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .activity-item {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .stats-overview,
  .quick-actions,
  .recent-activity {
    padding: 20px;
  }

  .section-title {
    font-size: 20px;
    justify-content: center;
  }
}
