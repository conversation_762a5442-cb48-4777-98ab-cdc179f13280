/* Global container */
.dashboard {
  display: grid;
  grid-template-columns: 250px 1fr;
  grid-template-areas: "sidebar main";
  min-height: 100vh;
  background:
    linear-gradient(135deg, #001040FF 0%, #001660FF 30%, #000a2e 70%, #001040FF 100%),
    radial-gradient(ellipse at top left, rgba(255, 107, 53, 0.15) 0%, transparent 60%),
    radial-gradient(ellipse at bottom right, rgba(0, 22, 96, 0.2) 0%, transparent 60%);
  font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
  overflow-x: hidden;
  position: relative;
}

.dashboard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 15% 25%, rgba(255, 107, 53, 0.12) 0%, transparent 45%),
    radial-gradient(circle at 85% 75%, rgba(0, 22, 96, 0.18) 0%, transparent 50%),
    radial-gradient(circle at 50% 10%, rgba(255, 107, 53, 0.08) 0%, transparent 40%),
    radial-gradient(circle at 20% 80%, rgba(0, 22, 96, 0.1) 0%, transparent 35%);
  pointer-events: none;
  z-index: 0;
  animation: backgroundShift 20s ease-in-out infinite;
}

@keyframes backgroundShift {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.8; }
}

/* Sidebar */
app-sidebar-recruiter {
  grid-area: sidebar;
  z-index: 100;
  position: relative;
}

/* Main content */
.dashboard-main {
  grid-area: main;
  display: flex;
  flex-direction: column;
  background:
    linear-gradient(145deg, rgba(255, 255, 255, 0.03) 0%, rgba(255, 255, 255, 0.01) 100%),
    rgba(255, 255, 255, 0.02);
  backdrop-filter: blur(25px) saturate(180%);
  border-radius: 0 0 0 32px;
  margin: 0;
  position: relative;
  z-index: 1;
  overflow-y: auto;
  max-height: 100vh;
  border: 1px solid rgba(255, 255, 255, 0.08);
  box-shadow:
    inset 0 1px 0 rgba(255, 255, 255, 0.1),
    0 20px 60px rgba(0, 0, 0, 0.3);
}

/* Header */
.dashboard-header {
  background:
    linear-gradient(135deg, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0.08) 100%),
    rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(30px) saturate(200%);
  border-bottom: 1px solid rgba(255, 255, 255, 0.15);
  padding: 28px 36px;
  position: sticky;
  top: 0;
  z-index: 50;
  box-shadow:
    0 8px 32px rgba(0, 16, 64, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  border-radius: 0 0 24px 24px;
  margin-bottom: 8px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.header-left {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

/* Title */
.dashboard-title {
  display: flex;
  align-items: center;
  gap: 16px;
  font-size: 36px;
  font-weight: 800;
  background: linear-gradient(135deg, #ffffff 0%, rgba(255, 255, 255, 0.9) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
  position: relative;
}

.dashboard-title::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, #FF6B35, transparent);
  border-radius: 2px;
}

.title-icon {
  font-size: 42px;
  width: 42px;
  height: 42px;
  background: linear-gradient(135deg, #FF6B35, #ff8c5a);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  filter: drop-shadow(0 4px 8px rgba(255, 107, 53, 0.4));
  animation: iconPulse 3s ease-in-out infinite;
}

@keyframes iconPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.dashboard-subtitle {
  color: rgba(255, 255, 255, 0.85);
  font-size: 18px;
  font-weight: 500;
  margin: 0;
  letter-spacing: 0.8px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Search container */
.search-container {
  display: flex;
  align-items: center;
  background:
    linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.08) 100%),
    rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px) saturate(180%);
  border: 1px solid rgba(255, 255, 255, 0.25);
  border-radius: 16px;
  padding: 14px 20px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.search-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 107, 53, 0.15), rgba(0, 22, 96, 0.12));
  opacity: 0;
  transition: opacity 0.4s ease;
  z-index: -1;
}

.search-container:hover::before {
  opacity: 1;
}

.search-container:focus-within::before {
  opacity: 1;
}

.search-container:hover,
.search-container:focus-within {
  border-color: rgba(255, 107, 53, 0.6);
  box-shadow:
    0 12px 40px rgba(255, 107, 53, 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
  transform: translateY(-3px) scale(1.02);
}

.search-icon {
  color: #FF6B35;
  font-size: 20px;
  margin-right: 8px;
  transition: all 0.3s ease;
}

.search-bar {
  border: none;
  background: transparent;
  padding: 0;
  width: 220px;
  font-size: 14px;
  color: #ffffff;
  outline: none;
  font-weight: 400;
}

.search-bar::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

/* Profile button */
.profile-button {
  display: flex;
  align-items: center;
  gap: 10px;
  background: linear-gradient(135deg, #001040FF, #001660FF);
  color: #ffffff !important;
  border-radius: 12px;
  padding: 12px 20px;
  font-size: 14px;
  font-weight: 600;
  box-shadow: 0 8px 24px rgba(0, 16, 64, 0.3);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.profile-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.profile-button:hover::before {
  left: 100%;
}

.profile-button:hover {
  background: linear-gradient(135deg, #FF6B35, #001660FF);
  box-shadow: 0 12px 32px rgba(255, 107, 53, 0.4);
  transform: translateY(-2px);
}

.profile-button mat-icon {
  font-size: 20px;
  width: 20px;
  height: 20px;
  color: #ffffff;
}

/* Header Buttons */
.notification-button,
.refresh-button,
.export-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  color: #ffffff;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.notification-button:hover,
.refresh-button:hover,
.export-button:hover {
  background: rgba(255, 107, 53, 0.2);
  border-color: rgba(255, 107, 53, 0.4);
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(255, 107, 53, 0.3);
}

.notification-badge {
  position: absolute;
  top: -4px;
  right: -4px;
  background: #FF6B35;
  color: #ffffff;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  font-size: 12px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.spinning {
  animation: spin 1s linear infinite;
}

/* Notifications Panel */
.notifications-panel {
  position: absolute;
  top: 100px;
  right: 32px;
  width: 400px;
  max-height: 500px;
  background:
    linear-gradient(145deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.08) 100%),
    rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(25px) saturate(200%);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  box-shadow:
    0 20px 60px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  z-index: 1000;
  overflow: hidden;
}

.notifications-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.notifications-header h3 {
  color: #ffffff;
  font-size: 18px;
  font-weight: 600;
  margin: 0;
}

.notifications-list {
  max-height: 400px;
  overflow-y: auto;
  padding: 16px;
}

.notification-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 16px;
  margin-bottom: 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border-left: 4px solid;
  transition: all 0.3s ease;
}

.notification-item:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateX(4px);
}

.notification-item.success {
  border-left-color: #22c55e;
}

.notification-item.info {
  border-left-color: #3b82f6;
}

.notification-item.warning {
  border-left-color: #f59e0b;
}

.notification-item.error {
  border-left-color: #ef4444;
}

.notification-content {
  flex: 1;
}

.notification-content h4 {
  color: #ffffff;
  font-size: 14px;
  font-weight: 600;
  margin: 0 0 4px 0;
}

.notification-content p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 13px;
  margin: 0 0 8px 0;
  line-height: 1.4;
}

.notification-time {
  color: rgba(255, 255, 255, 0.5);
  font-size: 11px;
}

/* Card container */
.card-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  padding: 32px;
}

/* Statistics Overview */
.stats-overview {
  padding: 32px;
  margin-bottom: 24px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
}

.stat-card {
  background:
    linear-gradient(145deg, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0.06) 100%),
    rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(25px) saturate(200%);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 20px;
  padding: 28px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  border-left: 5px solid;
  box-shadow:
    0 10px 40px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.08));
  opacity: 0;
  transition: opacity 0.4s ease;
  z-index: -1;
}

.stat-card::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transform: rotate(45deg);
  transition: transform 0.6s ease;
  opacity: 0;
}

.stat-card:hover::before {
  opacity: 1;
}

.stat-card:hover::after {
  opacity: 1;
  transform: rotate(45deg) translate(50%, 50%);
}

.stat-card:hover {
  transform: translateY(-6px) scale(1.02);
  box-shadow:
    0 20px 60px rgba(0, 0, 0, 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.25);
}

.stat-content {
  position: relative;
  z-index: 1;
}

.stat-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.stat-header mat-icon {
  font-size: 24px;
  width: 24px;
  height: 24px;
}

.stat-label {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-value {
  font-size: 42px;
  font-weight: 800;
  background: linear-gradient(135deg, #ffffff 0%, rgba(255, 255, 255, 0.9) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 12px;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
  position: relative;
  display: inline-block;
  animation: statValueGlow 2s ease-in-out infinite alternate;
}

@keyframes statValueGlow {
  0% {
    filter: drop-shadow(0 0 5px rgba(255, 255, 255, 0.3));
  }
  100% {
    filter: drop-shadow(0 0 15px rgba(255, 255, 255, 0.6));
  }
}

.stat-change {
  font-size: 12px;
  font-weight: 500;
  padding: 4px 8px;
  border-radius: 6px;
  display: inline-block;
}

.stat-change.positive {
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
}

.stat-change.neutral {
  background: rgba(156, 163, 175, 0.2);
  color: #9ca3af;
}

/* Quick Actions */
.quick-actions {
  padding: 32px;
  margin-bottom: 24px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 24px;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 24px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.section-title mat-icon {
  font-size: 28px;
  width: 28px;
  height: 28px;
  color: #FF6B35;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.action-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 24px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  gap: 16px;
}

.action-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.action-card:hover::before {
  opacity: 1;
}

.action-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.2);
  border-color: rgba(255, 255, 255, 0.2);
}

.action-icon {
  width: 56px;
  height: 56px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
}

.action-icon mat-icon {
  font-size: 28px;
  width: 28px;
  height: 28px;
  color: #ffffff;
}

.action-content {
  flex: 1;
}

.action-content h3 {
  font-size: 18px;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 8px 0;
}

.action-content p {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
  line-height: 1.4;
}

.action-arrow {
  color: rgba(255, 255, 255, 0.5);
  font-size: 20px;
  width: 20px;
  height: 20px;
  transition: all 0.3s ease;
}

.action-card:hover .action-arrow {
  color: #ffffff;
  transform: translateX(4px);
}

/* Recent Activity */
.recent-activity {
  padding: 32px;
  margin-bottom: 24px;
}

.activity-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
}

.activity-section {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 24px;
  transition: all 0.3s ease;
}

.activity-section:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
}

.activity-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 18px;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.activity-title mat-icon {
  font-size: 22px;
  width: 22px;
  height: 22px;
  color: #FF6B35;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.activity-item:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateX(4px);
}

.activity-icon {
  width: 40px;
  height: 40px;
  background: rgba(255, 107, 53, 0.2);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.activity-icon mat-icon {
  font-size: 20px;
  width: 20px;
  height: 20px;
  color: #FF6B35;
}

.activity-details {
  flex: 1;
}

.activity-details h4 {
  font-size: 16px;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 4px 0;
}

.activity-details p {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  margin: 0 0 4px 0;
}

.activity-date {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.5);
}

.activity-status {
  padding: 4px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  background: rgba(156, 163, 175, 0.2);
  color: #9ca3af;
}

.activity-status.active {
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
}

.activity-score {
  padding: 4px 12px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  background: rgba(59, 130, 246, 0.2);
  color: #3b82f6;
}

/* Loading */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: rgba(255, 255, 255, 0.7);
}

.loading-spinner {
  animation: spin 2s linear infinite;
  margin-bottom: 16px;
}

.loading-spinner mat-icon {
  font-size: 32px;
  width: 32px;
  height: 32px;
  color: #FF6B35;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Responsive */
@media (max-width: 1024px) {
  .dashboard {
    grid-template-columns: 220px 1fr;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .header-right {
    width: 100%;
    justify-content: space-between;
  }

  .search-bar {
    width: 180px;
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
  }

  .actions-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 16px;
  }

  .activity-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .stats-overview,
  .quick-actions,
  .recent-activity {
    padding: 24px;
  }
}

@media (max-width: 768px) {
  .dashboard {
    grid-template-columns: 1fr;
    grid-template-areas: "main";
  }

  app-sidebar-recruiter {
    display: none;
  }

  .dashboard-main {
    border-radius: 0;
  }

  .dashboard-header {
    padding: 16px 20px;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-left {
    text-align: center;
  }

  .dashboard-title {
    font-size: 28px;
    justify-content: center;
  }

  .header-right {
    flex-direction: column;
    gap: 12px;
  }

  .search-container {
    width: 100%;
  }

  .search-bar {
    width: 100%;
  }

  .profile-button {
    width: 100%;
    justify-content: center;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .stat-value {
    font-size: 28px;
  }

  .actions-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .action-card {
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }

  .activity-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .activity-item {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .stats-overview,
  .quick-actions,
  .recent-activity {
    padding: 20px;
  }

  .section-title {
    font-size: 20px;
    justify-content: center;
  }
}
