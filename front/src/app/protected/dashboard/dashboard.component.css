/* Global container - Executive Style */
.dashboard {
  display: grid;
  grid-template-columns: 250px 1fr;
  grid-template-areas: "sidebar main";
  min-height: 100vh;
  background:
    linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 25%, #2a2a2a 50%, #1a1a1a 75%, #0a0a0a 100%),
    radial-gradient(ellipse at top, rgba(255, 107, 53, 0.05) 0%, transparent 60%);
  font-family: 'SF Pro Display', 'Inter', 'Helvetica Neue', sans-serif;
  overflow-x: hidden;
  position: relative;
}

.dashboard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    linear-gradient(0deg, transparent 24px, rgba(255, 255, 255, 0.01) 25px, rgba(255, 255, 255, 0.01) 26px, transparent 27px, transparent 49px, rgba(255, 255, 255, 0.01) 50px, rgba(255, 255, 255, 0.01) 51px, transparent 52px),
    linear-gradient(90deg, transparent 24px, rgba(255, 255, 255, 0.01) 25px, rgba(255, 255, 255, 0.01) 26px, transparent 27px, transparent 49px, rgba(255, 255, 255, 0.01) 50px, rgba(255, 255, 255, 0.01) 51px, transparent 52px);
  pointer-events: none;
  z-index: 0;
}

/* Sidebar */
app-sidebar-recruiter {
  grid-area: sidebar;
  z-index: 100;
  position: relative;
}

/* Main content - Executive Panel */
.dashboard-main {
  grid-area: main;
  display: flex;
  flex-direction: column;
  background:
    linear-gradient(145deg, rgba(255, 255, 255, 0.03) 0%, rgba(255, 255, 255, 0.01) 100%),
    rgba(20, 20, 20, 0.8);
  backdrop-filter: blur(40px) saturate(120%);
  border-radius: 0;
  margin: 0;
  position: relative;
  z-index: 1;
  overflow-y: auto;
  max-height: 100vh;
  border-left: 1px solid rgba(255, 255, 255, 0.08);
  box-shadow:
    inset 4px 0 8px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

/* Header - Executive Command Center */
.dashboard-header {
  background:
    linear-gradient(180deg, rgba(10, 10, 10, 0.95) 0%, rgba(20, 20, 20, 0.9) 100%);
  backdrop-filter: blur(20px) saturate(100%);
  border-bottom: 1px solid rgba(255, 107, 53, 0.2);
  padding: 32px 40px;
  position: sticky;
  top: 0;
  z-index: 50;
  box-shadow:
    0 4px 20px rgba(0, 0, 0, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.03);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.header-left {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

/* Title - Executive Branding */
.dashboard-title {
  display: flex;
  align-items: center;
  gap: 16px;
  font-size: 32px;
  font-weight: 700;
  color: #ffffff;
  margin: 0;
  letter-spacing: -0.5px;
  position: relative;
}

.dashboard-title::after {
  content: '';
  position: absolute;
  bottom: -6px;
  left: 0;
  width: 60px;
  height: 2px;
  background: linear-gradient(90deg, #FF6B35, transparent);
  border-radius: 1px;
}

.title-icon {
  font-size: 32px;
  width: 32px;
  height: 32px;
  color: #FF6B35;
  opacity: 0.9;
}

.dashboard-subtitle {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  font-weight: 400;
  margin: 0;
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

/* Search container - Executive Search */
.search-container {
  display: flex;
  align-items: center;
  background: rgba(40, 40, 40, 0.6);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 12px 16px;
  transition: all 0.3s ease;
  min-width: 280px;
}

.search-container:hover,
.search-container:focus-within {
  border-color: rgba(255, 107, 53, 0.4);
  background: rgba(50, 50, 50, 0.7);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.search-icon {
  color: #FF6B35;
  font-size: 20px;
  margin-right: 8px;
  transition: all 0.3s ease;
}

.search-bar {
  border: none;
  background: transparent;
  padding: 0;
  width: 220px;
  font-size: 14px;
  color: #ffffff;
  outline: none;
  font-weight: 400;
}

.search-bar::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

/* Profile button - Executive Access */
.profile-button {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(40, 40, 40, 0.8);
  color: #ffffff !important;
  border-radius: 6px;
  padding: 10px 16px;
  font-size: 13px;
  font-weight: 500;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.profile-button:hover {
  background: rgba(255, 107, 53, 0.2);
  border-color: rgba(255, 107, 53, 0.4);
}

.profile-button mat-icon {
  font-size: 20px;
  width: 20px;
  height: 20px;
  color: #ffffff;
}

/* Header Buttons */
.notification-button,
.refresh-button,
.export-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  color: #ffffff;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.notification-button:hover,
.refresh-button:hover,
.export-button:hover {
  background: rgba(255, 107, 53, 0.2);
  border-color: rgba(255, 107, 53, 0.4);
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(255, 107, 53, 0.3);
}

.notification-badge {
  position: absolute;
  top: -4px;
  right: -4px;
  background: #FF6B35;
  color: #ffffff;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  font-size: 12px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.spinning {
  animation: spin 1s linear infinite;
}

/* Notifications Panel */
.notifications-panel {
  position: absolute;
  top: 100px;
  right: 32px;
  width: 400px;
  max-height: 500px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  overflow: hidden;
}

.notifications-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.notifications-header h3 {
  color: #ffffff;
  font-size: 18px;
  font-weight: 600;
  margin: 0;
}

.notifications-list {
  max-height: 400px;
  overflow-y: auto;
  padding: 16px;
}

.notification-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 16px;
  margin-bottom: 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border-left: 4px solid;
  transition: all 0.3s ease;
}

.notification-item:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateX(4px);
}

.notification-item.success {
  border-left-color: #22c55e;
}

.notification-item.info {
  border-left-color: #3b82f6;
}

.notification-item.warning {
  border-left-color: #f59e0b;
}

.notification-item.error {
  border-left-color: #ef4444;
}

.notification-content {
  flex: 1;
}

.notification-content h4 {
  color: #ffffff;
  font-size: 14px;
  font-weight: 600;
  margin: 0 0 4px 0;
}

.notification-content p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 13px;
  margin: 0 0 8px 0;
  line-height: 1.4;
}

.notification-time {
  color: rgba(255, 255, 255, 0.5);
  font-size: 11px;
}

/* Card container */
.card-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  padding: 32px;
}

/* Statistics Overview */
.stats-overview {
  padding: 32px;
  margin-bottom: 24px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
}

.stat-card {
  background:
    linear-gradient(145deg, rgba(30, 30, 30, 0.9) 0%, rgba(20, 20, 20, 0.95) 100%);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 12px;
  padding: 28px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
  border-left: 3px solid;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow:
    0 12px 40px rgba(0, 0, 0, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.08);
  border-left-color: #FF6B35;
}

.stat-content {
  position: relative;
  z-index: 1;
}

.stat-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.stat-header mat-icon {
  font-size: 24px;
  width: 24px;
  height: 24px;
  color: rgba(255, 255, 255, 0.7);
}

.stat-label {
  color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.stat-value {
  font-size: 36px;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 8px;
  line-height: 1;
}

.stat-change {
  font-size: 12px;
  font-weight: 500;
  padding: 4px 8px;
  border-radius: 6px;
  display: inline-block;
}

.stat-change.positive {
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
}

.stat-change.neutral {
  background: rgba(156, 163, 175, 0.2);
  color: #9ca3af;
}

/* Quick Actions */
.quick-actions {
  padding: 32px;
  margin-bottom: 24px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 24px;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 24px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.section-title mat-icon {
  font-size: 28px;
  width: 28px;
  height: 28px;
  color: #FF6B35;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.action-card {
  background:
    linear-gradient(145deg, rgba(30, 30, 30, 0.8) 0%, rgba(20, 20, 20, 0.9) 100%);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 10px;
  padding: 24px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 16px;
  position: relative;
  overflow: hidden;
  box-shadow:
    0 4px 20px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

.action-card:hover {
  transform: translateY(-2px);
  box-shadow:
    0 8px 30px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 107, 53, 0.3);
}

.action-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  background: rgba(255, 107, 53, 0.1);
  border: 1px solid rgba(255, 107, 53, 0.2);
}

.action-icon mat-icon {
  font-size: 24px;
  width: 24px;
  height: 24px;
  color: #FF6B35;
}

.action-content {
  flex: 1;
}

.action-content h3 {
  font-size: 16px;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 4px 0;
  letter-spacing: -0.2px;
}

.action-content p {
  font-size: 13px;
  color: rgba(255, 255, 255, 0.6);
  margin: 0;
  line-height: 1.4;
}

.action-arrow {
  color: rgba(255, 255, 255, 0.4);
  font-size: 18px;
  width: 18px;
  height: 18px;
  transition: all 0.3s ease;
}

.action-card:hover .action-arrow {
  color: #FF6B35;
  transform: translateX(4px);
}

/* Recent Activity */
.recent-activity {
  padding: 32px;
  margin-bottom: 24px;
}

.activity-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
}

.activity-section {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 24px;
  transition: all 0.3s ease;
}

.activity-section:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
}

.activity-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 18px;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.activity-title mat-icon {
  font-size: 22px;
  width: 22px;
  height: 22px;
  color: #FF6B35;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.activity-item:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateX(4px);
}

.activity-icon {
  width: 40px;
  height: 40px;
  background: rgba(255, 107, 53, 0.2);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.activity-icon mat-icon {
  font-size: 20px;
  width: 20px;
  height: 20px;
  color: #FF6B35;
}

.activity-details {
  flex: 1;
}

.activity-details h4 {
  font-size: 16px;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 4px 0;
}

.activity-details p {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  margin: 0 0 4px 0;
}

.activity-date {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.5);
}

.activity-status {
  padding: 4px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  background: rgba(156, 163, 175, 0.2);
  color: #9ca3af;
}

.activity-status.active {
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
}

.activity-score {
  padding: 4px 12px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  background: rgba(59, 130, 246, 0.2);
  color: #3b82f6;
}

/* Loading */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: rgba(255, 255, 255, 0.7);
}

.loading-spinner {
  animation: spin 2s linear infinite;
  margin-bottom: 16px;
}

.loading-spinner mat-icon {
  font-size: 32px;
  width: 32px;
  height: 32px;
  color: #FF6B35;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Responsive */
@media (max-width: 1024px) {
  .dashboard {
    grid-template-columns: 220px 1fr;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .header-right {
    width: 100%;
    justify-content: space-between;
  }

  .search-bar {
    width: 180px;
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
  }

  .actions-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 16px;
  }

  .activity-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .stats-overview,
  .quick-actions,
  .recent-activity {
    padding: 24px;
  }
}

@media (max-width: 768px) {
  .dashboard {
    grid-template-columns: 1fr;
    grid-template-areas: "main";
  }

  app-sidebar-recruiter {
    display: none;
  }

  .dashboard-main {
    border-radius: 0;
  }

  .dashboard-header {
    padding: 16px 20px;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-left {
    text-align: center;
  }

  .dashboard-title {
    font-size: 28px;
    justify-content: center;
  }

  .header-right {
    flex-direction: column;
    gap: 12px;
  }

  .search-container {
    width: 100%;
  }

  .search-bar {
    width: 100%;
  }

  .profile-button {
    width: 100%;
    justify-content: center;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .stat-value {
    font-size: 28px;
  }

  .actions-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .action-card {
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }

  .activity-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .activity-item {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .stats-overview,
  .quick-actions,
  .recent-activity {
    padding: 20px;
  }

  .section-title {
    font-size: 20px;
    justify-content: center;
  }
}
