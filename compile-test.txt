Test de compilation - Méthodes corrigées dans UserController:

1. ✅ Suppression de la méthode getUserById dupliquée
2. ✅ Correction du mapping: @GetMapping("/profile/{userId}")
3. ✅ Correction de profile.getTitle() → titre dynamique basé sur firstName + lastName
4. ✅ Utilisation du service userProfileService au lieu du repository direct

Erreurs corrigées:
- method getUserById(String) is already defined in class UserController
- cannot find symbol: method getTitle()

Le contrôleur devrait maintenant compiler sans erreurs.
